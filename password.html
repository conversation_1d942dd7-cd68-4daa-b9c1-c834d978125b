<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALT Wallet - Set Password</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            box-sizing: border-box;
        }
        /* Custom dark blue for the VALT theme */
        .bg-valt-dark-blue {
            background-color: #1A3E7D;
        }
        .hover\:bg-valt-dark-blue-darker:hover {
            background-color: #153466; /* A slightly darker shade for hover effect */
        }
        .focus\:ring-valt-dark-blue {
            --tw-ring-color: #1A3E7D;
        }
    </style>
</head>
<body class="flex flex-col items-center bg-gray-900 h-screen text-white">
    <div class="p-8 w-full max-w-full text-center flex-grow flex flex-col justify-between items-center relative">
        <div class="absolute top-4 left-4">
            <a href="#" class="flex items-center text-gray-300 hover:text-white transition duration-200">
                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back
            </a>
        </div>

        <div class="flex flex-col items-center justify-center flex-grow">
            <div class="mb-8 p-4 bg-valt-dark-blue rounded-full">
                <svg class="h-16 w-16 text-white mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2v5a2 2 0 01-2 2h-5a2 2 0 01-2-2V9a2 2 0 012-2h5z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 10a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </div>

            <h1 class="text-3xl font-semibold mb-2">Set Your Password</h1>
            <p class="text-gray-300 text-base mb-8">This password will be used to unlock your VALT Wallet.</p>

            <div class="w-full max-w-sm mb-8">
                <div class="relative mb-4">
                    <label for="password" class="sr-only">New Password</label>
                    <input
                        type="password"
                        id="password"
                        placeholder="Enter new password"
                        class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition duration-200 pr-10"
                    >
                    <span
                        class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                        onclick="togglePasscodeVisibility('password', 'eye-icon-password')"
                    >
                        <svg id="eye-icon-password" class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                    </span>
                </div>

                <div class="relative">
                    <label for="confirm-password" class="sr-only">Confirm Password</label>
                    <input
                        type="password"
                        id="confirm-password"
                        placeholder="Confirm new password"
                        class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition duration-200 pr-10"
                    >
                    <span
                        class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                        onclick="togglePasscodeVisibility('confirm-password', 'eye-icon-confirm')"
                    >
                        <svg id="eye-icon-confirm" class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                    </span>
                    <div id="password-status" class="flex items-center mt-2" style="display: none;">
                        <svg id="status-icon" class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        </svg>
                        <span id="status-text" class="text-sm"></span>
                    </div>
                </div>
            </div>

            <div class="flex flex-col gap-4 w-full max-w-sm">
                <button class="bg-valt-dark-blue text-white p-3 rounded-lg font-medium hover:bg-valt-dark-blue-darker focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:ring-opacity-50 transition duration-200">
                    Continue
                </button>
            </div>
        </div>
    </div>

    <script>
        // Existing JavaScript for password validation
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirm-password');
        const passwordStatusDiv = document.getElementById('password-status');
        const statusIcon = document.getElementById('status-icon');
        const statusText = document.getElementById('status-text');

        function updatePasswordStatus() {
            const password = passwordInput.value;
            const confirmPassword = confirmPasswordInput.value;

            if (password.length === 0 && confirmPassword.length === 0) {
                passwordStatusDiv.style.display = 'none';
                return;
            }

            passwordStatusDiv.style.display = 'flex';

            if (password !== confirmPassword) {
                setStatus('Passwords do not match', false);
            } else if (password.length < 8) {
                setStatus('Password must be at least 8 characters', false);
            } else {
                setStatus('Passwords match and meet requirements', true);
            }
        }

        function setStatus(message, isSuccess) {
            statusText.textContent = message;
            statusIcon.innerHTML = isSuccess
                ? '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>'
                : '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';

            statusText.classList.toggle('text-green-400', isSuccess);
            statusText.classList.toggle('text-red-400', !isSuccess);
            statusIcon.classList.toggle('text-green-400', isSuccess);
            statusIcon.classList.toggle('text-red-400', !isSuccess);
        }

        passwordInput.addEventListener('input', updatePasswordStatus);
        confirmPasswordInput.addEventListener('input', updatePasswordStatus);

        // New JavaScript for password visibility toggle
        function togglePasscodeVisibility(inputId, iconId) {
            const inputField = document.getElementById(inputId);
            const eyeIcon = document.getElementById(iconId);

            if (inputField.type === 'password') {
                inputField.type = 'text';
                eyeIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.875A10.02 10.02 0 0112 19c-4.478 0-8.268-2.943-9.542-7 1.274-4.057 5.064-7 9.542-7 1.704 0 3.336.37 4.776 1.054M4.776 4.776L19.224 19.224"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>'; // Closed eye SVG
            } else {
                inputField.type = 'password';
                eyeIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>'; // Open eye SVG
            }
        }
    </script>
</body>
</html>