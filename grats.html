<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Congratulations! - VALT Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden; /* Prevent scroll on confetti */
            background-color: #1a202c; /* Ensure body background matches your dark theme if it's a standalone page */
            color: #ffffff;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #3C5B97 0%, #1A3E7D 100%); 
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%; /* Make gradient cover the full height for this page */
            z-index: -1;
            opacity: 0.8; /* Slightly less opaque to let the dark background show through a bit */
        }

        .main-content {
            flex-grow: 1;
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 1.5rem;
            text-align: center;
        }

        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Confetti styles */
        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: #fff; /* Default color, overridden by JS */
            opacity: 0;
            animation: confetti-fall linear forwards;
            pointer-events: none; /* Allow clicks to pass through */
        }

        @keyframes confetti-fall {
            0% {
                transform: translateY(0) rotateZ(0deg) scale(1);
                opacity: 0.8;
            }
            100% {
                transform: translateY(100vh) rotateZ(720deg) scale(0.5);
                opacity: 0;
            }
        }
    </style>
</head>
<body class="bg-gray-900 text-white">

    <div class="gradient-bg">
        <div class="absolute top-0 left-0 w-full h-full opacity-20">
            <div class="absolute top-4 left-4 w-32 h-32 bg-white rounded-full opacity-10"></div>
            <div class="absolute bottom-4 right-4 w-48 h-48 bg-white rounded-full opacity-5"></div>
        </div>
    </div>

    <div class="main-content">
        <div class="glass-effect rounded-3xl p-8 max-w-sm w-full relative overflow-hidden">
            <div id="confetti-container" class="absolute inset-0 pointer-events-none overflow-hidden"></div>

            <div class="flex flex-col items-center justify-center space-y-6">
                <div class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center animate-pulse-slow">
                    <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>

                <h1 class="text-4xl font-bold text-white">Congratulations!</h1>
                <p class="text-lg text-gray-300">Your VALT Wallet has been successfully created.</p>
                <p class="text-md text-gray-400">You're now ready to manage your digital assets with confidence.</p>

                <button onclick="window.location.href='wallet.html'" class="mt-8 w-full py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-xl transition-colors duration-300 shadow-lg">
                    Go to My Wallet
                </button>
            </div>
        </div>
    </div>

    <script>
        function createConfetti() {
            const confettiColors = ['#FBBF24', '#EF4444', '#3B82F6', '#8B5CF6', '#10B981']; // Yellow, Red, Blue, Purple, Green
            const container = document.getElementById('confetti-container');

            for (let i = 0; i < 50; i++) { // Generate 50 pieces of confetti
                const confetti = document.createElement('div');
                confetti.classList.add('confetti');
                confetti.style.left = `${Math.random() * 100}%`;
                confetti.style.top = `${Math.random() * -20}%`; // Start above the screen
                confetti.style.backgroundColor = confettiColors[Math.floor(Math.random() * confettiColors.length)];
                confetti.style.animationDelay = `${Math.random() * 2}s`; // Stagger animation start
                confetti.style.animationDuration = `${3 + Math.random() * 2}s`; // Vary animation duration
                confetti.style.transform = `rotateZ(${Math.random() * 360}deg)`; // Initial random rotation
                container.appendChild(confetti);

                // Remove confetti after it falls to prevent accumulation
                confetti.addEventListener('animationend', () => {
                    confetti.remove();
                });
            }
        }

        // Trigger confetti when the page loads
        document.addEventListener('DOMContentLoaded', createConfetti);
    </script>
</body>
</html>