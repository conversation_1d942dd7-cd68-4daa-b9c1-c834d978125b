<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALT Wallet - Unlock</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            box-sizing: border-box;
        }
        /* Custom dark blue for the VALT theme */
        .bg-valt-dark-blue {
            background-color: #1A3E7D;
        }
        .hover\:bg-valt-dark-blue-darker:hover {
            background-color: #153466; /* A slightly darker shade for hover effect */
        }
        .focus\:ring-valt-dark-blue {
            --tw-ring-color: #1A3E7D;
        }
        /* No longer need body.valt-theme here as bg-gray-900 is applied directly */
    </style>
</head>
<body class="flex flex-col items-center justify-center h-screen bg-gray-900 text-white"> <div class="p-8 w-full max-w-sm text-center">
        <div class="mb-8 p-4 bg-valt-dark-blue rounded-full mx-auto" style="width: fit-content;">
            <svg class="h-16 w-16 text-white mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2v5a2 2 0 01-2 2h-5a2 2 0 01-2-2V9a2 2 0 012-2h5z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 10a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
        </div>

        <h1 class="text-4xl font-semibold mb-8">VALT Wallet</h1>

        <div class="relative w-full mb-6">
            <input
                type="password"
                id="password"
                placeholder="Enter your password"
                class="w-full p-3 pr-10 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-gray-400 border border-gray-600"
            >
            <span
                class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                onclick="togglePasswordVisibility('password', 'eye-icon-password')"
            >
                <svg id="eye-icon-password" class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
            </span>
        </div>

        <button class="bg-valt-dark-blue text-white p-3 rounded-lg font-medium w-full hover:bg-valt-dark-blue-darker focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:ring-opacity-50 transition duration-200 mb-4">
            Unlock
        </button>

        <a href="#" class="text-gray-400 hover:text-white text-sm transition duration-200">
            Forgot your password?
        </a>
    </div>

    <script>
        function togglePasswordVisibility(inputId, iconId) {
            const inputField = document.getElementById(inputId);
            const eyeIcon = document.getElementById(iconId);

            if (inputField.type === 'password') {
                inputField.type = 'text';
                eyeIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.875A10.02 10.02 0 0112 19c-4.478 0-8.268-2.943-9.542-7 1.274-4.057 5.064-7 9.542-7 1.704 0 3.336.37 4.776 1.054M4.776 4.776L19.224 19.224"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>';
            } else {
                inputField.type = 'password';
                eyeIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>';
            }
        }
    </script>
</body>
</html>