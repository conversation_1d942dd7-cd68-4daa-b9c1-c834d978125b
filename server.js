import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES Modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const port = process.env.PORT || 3370;

// Set EJS as the view engine
app.set('view engine', 'ejs');
// Set the views directory where your .ejs files will be located
app.set('views', path.join(__dirname, 'views')); // Create a 'views' folder for your EJS templates

// Serve static files (CSS, JS, images, etc.) from a 'public' directory
// It's good practice to separate static assets from your view templates
app.use(express.static(path.join(__dirname, 'public'))); // Create a 'public' folder for static assets

// Route for the homepage
app.get('/', (req, res) => {
    // Instead of res.sendFile, use res.render to render an EJS template
    res.render('index', {
        title: 'VALT Wallet - Home', // Example of passing data to your template
        message: 'Welcome to the VALT Wallet!', // Another example
    });
});

// Start the server
app.listen(port, () => {
    console.log(`VALT Wallet app listening on http://localhost:${port}`);
});