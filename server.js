import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url'; // Required for __dirname equivalent in ES Modules

// Get __dirname equivalent in ES Modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const port = process.env.PORT || 3370;

// Serve static files from the current directory
app.use(express.static(path.join(__dirname)));

// Route for the homepage
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Start the server
app.listen(port, () => {
    console.log(`VALT Wallet app listening on http://localhost:${port}`);
});