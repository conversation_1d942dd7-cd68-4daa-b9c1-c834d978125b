<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALT Wallet - Send/Receive</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            box-sizing: border-box;
        }
        /* Custom dark blue for the VALT theme */
        .bg-valt-dark-blue {
            background-color: #1A3E7D;
        }
        .hover\:bg-valt-dark-blue-darker:hover {
            background-color: #153466; /* A slightly darker shade for hover effect */
        }
        .focus\:ring-valt-dark-blue {
            --tw-ring-color: #1A3E7D;
        }
        /* Custom styles for the dark button background from the image */
        .btn-dark-bg {
            background-color: #2F3E5E; /* A shade close to the button background in the image */
            color: #FFFFFF;
        }
        .hover\:btn-dark-bg-hover:hover {
            background-color: #3B4D6F; /* Slightly lighter on hover for better interaction */
        }
        .text-dark-text-light {
            color: #D1D5DB; /* A lighter gray for secondary text, similar to the image */
        }
        .input-dark-border {
            border-color: #4A5568; /* Darker border for input fields */
        }
        .active-tab {
            border-bottom: 2px solid #1A3E7D; /* Highlight active tab with your custom blue */
            color: #1A3E7D;
        }

        /* Additional styles for QR code placeholder */
        .qr-code-placeholder {
            background-color: #3B4D6F; /* A darker gray for the QR code area */
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            color: #8894AE;
            border-radius: 0.5rem;
            height: 160px; /* Fixed height for consistent look */
            width: 160px; /* Fixed width for consistent look */
        }
        /* Style for the dropdown arrow (mimicking select behavior) */
        .select-wrapper {
            position: relative;
        }
        .select-wrapper::after {
            content: '▼'; /* Unicode down arrow */
            font-size: 0.75rem;
            color: #cbd5e0; /* light gray */
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none; /* Make sure clicks go through to the select element */
        }
    </style>
</head>
<body class="bg-gray-900 text-white flex flex-col min-h-screen">
    <div class="p-4">
        <a href="dashboard.html" class="flex items-center text-gray-300 hover:text-white transition duration-200">
            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back
        </a>
    </div>

    <div class="flex-grow flex flex-col items-center justify-center p-4 pt-0 md:pt-4">
        <div class="text-center max-w-md mx-auto w-full">

            <h1 class="text-3xl font-semibold mb-8">Send / Receive</h1>

            <div class="mb-8 border-b border-gray-700">
                <nav class="flex justify-center -mb-px" role="tablist">
                    <button id="tab-send" class="py-3 px-6 text-lg font-medium text-gray-400 hover:text-white border-b-2 border-transparent hover:border-valt-dark-blue focus:outline-none transition-colors duration-200 active-tab" role="tab" aria-selected="true" aria-controls="panel-send">
                        Send
                    </button>
                    <button id="tab-receive" class="py-3 px-6 text-lg font-medium text-gray-400 hover:text-white border-b-2 border-transparent hover:border-valt-dark-blue focus:outline-none transition-colors duration-200" role="tab" aria-selected="false" aria-controls="panel-receive">
                        Receive
                    </button>
                </nav>
            </div>

            <div id="tab-content">
                <div id="panel-send" role="tabpanel" aria-labelledby="tab-send" class="tab-panel active">
                    <form class="space-y-6">
                        <div>
                            <label for="recipient-address" class="block text-left text-sm font-medium text-gray-300 mb-2">Recipient Address:</label>
                            <input type="text" id="recipient-address" class="block w-full px-4 py-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition-colors duration-200" placeholder="Enter recipient address (e.g., 0x...)">
                            <p class="text-sm text-gray-500 mt-2 text-left">Always double-check the address before sending.</p>
                        </div>

                        <div>
                            <label for="coin-select" class="block text-left text-sm font-medium text-gray-300 mb-2">Select Coin:</label>
                            <div class="select-wrapper">
                                <select id="coin-select" class="block w-full px-4 py-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition-colors duration-200 appearance-none pr-10">
                                    <option value="ETH">Ethereum (ETH)</option>
                                    <option value="BTC">Bitcoin (BTC)</option>
                                    <option value="USDT">Tether (USDT)</option>
                                    <option value="DAI">Dai (DAI)</option>
                                    <option value="VALT_TOKEN">VALT Token (VLT)</option>
                                    </select>
                            </div>
                            <p id="available-balance-text" class="text-sm text-gray-500 mt-2 text-left">Available: 0.12345 ETH</p>
                        </div>

                        <div>
                            <label for="amount" class="block text-left text-sm font-medium text-gray-300 mb-2">Amount:</label>
                            <div class="relative">
                                <input type="number" id="amount" step="any" min="0" class="block w-full pr-16 px-4 py-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition-colors duration-200" placeholder="0.00">
                                <span id="currency-unit" class="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 font-semibold">ETH</span> </div>
                        </div>

                        <div>
                            <label for="network-fee" class="block text-left text-sm font-medium text-gray-300 mb-2">Network Fee (Estimated):</label>
                            <input type="text" id="network-fee" class="block w-full px-4 py-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition-colors duration-200 cursor-not-allowed" value="0.0001 ETH (~$0.25)" readonly>
                            <p class="text-sm text-gray-500 mt-2 text-left">Fees may vary based on network congestion.</p>
                        </div>

                        <button type="submit" class="w-full btn-dark-bg hover:btn-dark-bg-hover text-white font-semibold py-3 px-6 rounded-lg shadow-lg transition duration-300 ease-in-out transform hover:scale-105">
                            Review Transaction
                        </button>
                    </form>
                </div>

                <div id="panel-receive" role="tabpanel" aria-labelledby="tab-receive" class="tab-panel hidden">
                    <div class="flex flex-col items-center space-y-6">
                        <p class="text-lg text-gray-300">Share your address to receive funds.</p>

                        <div class="qr-code-placeholder">
                            QR Code will go here
                        </div>

                        <div class="w-full">
                            <label for="wallet-address" class="block text-left text-sm font-medium text-gray-300 mb-2">Your Wallet Address:</label>
                            <div class="relative">
                                <input type="text" id="wallet-address" class="block w-full pr-12 px-4 py-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition-colors duration-200 cursor-copy" value="******************************************" readonly>
                                <button class="absolute right-0 top-0 h-full w-12 flex items-center justify-center text-gray-400 hover:text-white transition duration-200" title="Copy to clipboard" onclick="copyAddress()">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5v10h10L18 5m-10 0h10a2 2 0 012 2v2M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5v10h10L18 5"></path>
                                    </svg>
                                </button>
                            </div>
                            <span id="copy-feedback" class="text-sm text-green-400 mt-2 hidden">Copied!</span>
                        </div>

                        <button class="w-full btn-dark-bg hover:btn-dark-bg-hover text-white font-semibold py-3 px-6 rounded-lg shadow-lg transition duration-300 ease-in-out transform hover:scale-105">
                            Share Address
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const tabs = document.querySelectorAll('[role="tab"]');
            const tabPanels = document.querySelectorAll('.tab-panel');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Deactivate all tabs
                    tabs.forEach(t => {
                        t.classList.remove('active-tab');
                        t.setAttribute('aria-selected', 'false');
                        t.classList.remove('text-valt-dark-blue'); // Remove color for inactive tabs
                        t.classList.add('text-gray-400'); // Add gray for inactive tabs
                    });

                    // Hide all tab panels
                    tabPanels.forEach(panel => panel.classList.add('hidden'));

                    // Activate the clicked tab
                    tab.classList.add('active-tab');
                    tab.setAttribute('aria-selected', 'true');
                    tab.classList.remove('text-gray-400'); // Remove gray
                    tab.classList.add('text-valt-dark-blue'); // Add blue for active tab

                    // Show the corresponding tab panel
                    const targetPanelId = tab.getAttribute('aria-controls');
                    document.getElementById(targetPanelId).classList.remove('hidden');
                });
            });

            // Set the first tab ("Send") as active by default on load
            const initialTab = document.getElementById('tab-send');
            if (initialTab) {
                initialTab.click(); // Simulate a click to activate the first tab and its content
            }

            // JavaScript for Copy Address functionality
            window.copyAddress = function() {
                const addressInput = document.getElementById('wallet-address');
                addressInput.select();
                addressInput.setSelectionRange(0, 99999); // For mobile devices

                navigator.clipboard.writeText(addressInput.value).then(() => {
                    const feedback = document.getElementById('copy-feedback');
                    feedback.classList.remove('hidden');
                    setTimeout(() => {
                        feedback.classList.add('hidden');
                    }, 2000); // Hide "Copied!" message after 2 seconds
                }).catch(err => {
                    console.error('Failed to copy text: ', err);
                });
            };

            // JavaScript for dynamically updating currency unit and available balance
            const coinSelect = document.getElementById('coin-select');
            const currencyUnitSpan = document.getElementById('currency-unit');
            const availableBalanceText = document.getElementById('available-balance-text');

            const balances = {
                'ETH': '0.12345 ETH',
                'BTC': '0.005 BTC',
                'USDT': '1250.75 USDT',
                'DAI': '500 DAI',
                'VALT_TOKEN': '10000 VLT'
            };

            coinSelect.addEventListener('change', () => {
                const selectedCoin = coinSelect.value;
                currencyUnitSpan.textContent = selectedCoin.replace('_TOKEN', ''); // Remove _TOKEN for display
                availableBalanceText.textContent = `Available: ${balances[selectedCoin] || 'N/A'}`;
            });

            // Trigger change event on load to set initial values
            coinSelect.dispatchEvent(new Event('change'));
        });
    </script>
</body>
</html>