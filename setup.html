<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALT Wallet - Setup</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            box-sizing: border-box;
        }
        /* Custom dark blue for the VALT theme - if used elsewhere */
        .bg-valt-dark-blue {
            background-color: #1A3E7D;
        }
        .hover\:bg-valt-dark-blue-darker:hover {
            background-color: #153466; /* A slightly darker shade for hover effect */
        }
        .focus\:ring-valt-dark-blue {
            --tw-ring-color: #1A3E7D;
        }
    </style>
</head>
<body class="flex flex-col items-center bg-gray-900 h-screen">
    <div class="p-8 rounded-xl shadow-2xl w-full max-w-full text-center border border-gray-700 flex-grow flex flex-col justify-center items-center">
        <!-- VALT Logo - Placeholder SVG (Updated to match VALT theme colors) -->
        <div class="mb-8">
            <svg class="h-24 w-24 mx-auto" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                <!-- Outer Hexagon - Dark Blue -->
                <path d="M50 0L0 25V75L50 100L100 75V25L50 0Z" fill="#1A3E7D"/>
                <!-- Inner Hexagon 1 - Slightly Lighter Blue -->
                <path d="M50 10L10 30V70L50 90L90 70V30L50 10Z" fill="#2A62F6"/>
                <!-- Inner Hexagon 2 - Lighter Blue -->
                <path d="M50 20L20 35V65L50 80L80 65V35L50 20Z" fill="#3B82F6"/>
                <!-- Inner Hexagon 3 - Even Lighter Blue -->
                <path d="M50 30L30 40V60L50 70L70 60V40L50 30Z" fill="#60A5FA"/>
                <!-- Innermost Hexagon - Lightest Blue -->
                <path d="M50 40L40 45V55L50 60L60 55V45L50 40Z" fill="#93C5FD"/>
                <!-- Inner 'H' shape (simplified for clarity, using white for contrast) -->
                <path d="M45 45H55V55H45V45Z" fill="#FFFFFF"/>
                <path d="M50 40L65 40L65 60L50 60L50 50L45 50L45 40L50 40Z" fill="#FFFFFF"/>
                <path d="M50 55L55 55L55 60L50 60L50 55Z" fill="#BFDBFE"/>
                <path d="M45 45L45 50L50 50L50 45L45 45Z" fill="#BFDBFE"/>
                <path d="M35 60L50 60L50 40L35 40L35 50L40 50L40 45L35 45L35 60Z" fill="#FFFFFF"/>
            </svg>
        </div>

        <h1 class="text-white text-3xl font-semibold mb-4">VALT Wallet</h1>

        <!-- Subtitle -->
        <p class="text-gray-300 text-lg mb-10 leading-relaxed max-w-sm mx-auto">
            Managing your digital wealth.
        </p>

        <!-- Question -->
        <p class="text-gray-300 text-base mb-6">
            Set up your new wallet.
        </p>

        <!-- Wallet Options Container -->
        <div class="flex flex-col md:flex-row gap-4 w-full max-w-md">
            <!-- Create New Wallet Card - Now a link -->
            <a href="create.html" class="flex-1 bg-gray-700 p-6 rounded-xl border border-gray-600 cursor-pointer hover:bg-gray-600 transition duration-200 flex flex-col items-center justify-center">
                <svg class="h-12 w-12 text-white mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span class="text-white font-medium">Create New Wallet</span>
            </a>

            <!-- Import Your Wallet Card - Now a link -->
            <a href="import.html" class="flex-1 bg-gray-700 p-6 rounded-xl border border-gray-600 cursor-pointer hover:bg-gray-600 transition duration-200 flex flex-col items-center justify-center">
                <svg class="h-12 w-12 text-white mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                <span class="text-white font-medium">Import Your Wallet</span>
            </a>
        </div>
    </div>
</body>
</html>