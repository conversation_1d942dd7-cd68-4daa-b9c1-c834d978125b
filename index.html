<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALT Wallet</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif; /* Ensuring Inter font is applied */
            box-sizing: border-box;
        }
        .bg-custom-dark-blue {
            background-color: #1A3E7D;
        }
        .hover\:bg-custom-dark-blue-darker:hover {
            background-color: #153466; /* A slightly darker shade for hover effect */
        }
    </style>
</head>
<body class="bg-gray-900 text-white flex items-center justify-center min-h-screen p-4">
    <div class="text-center max-w-md mx-auto"> <div class="w-24 h-24 mx-auto mb-8 bg-custom-dark-blue rounded-full flex items-center justify-center">
            <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
        </div>

        <h1 class="text-4xl font-semibold mb-4">VALT Wallet</h1>
        <p class="text-gray-400 text-lg mb-8">
            Effortless access to the new digital economy. Manage, send, and receive with confidence.
        </p>
        <a href="setup.html" class="inline-block">
            <button class="bg-custom-dark-blue hover:bg-custom-dark-blue-darker text-white font-semibold py-3 px-16 rounded-lg shadow-lg transition duration-300 ease-in-out transform hover:scale-105">
                Get Started
            </button>
        </a>
    </div>
</body>
</html>