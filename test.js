import { KeyGen } from './KeyGen.js'; // Adjust the path if KeyGen.js is in a different directory

async function runExample() {
    console.log("Generating a new HD Wallet...");
    try {
        const wallet = await KeyGen.generateHDWallet();

        console.log("\n--- Wallet Details ---");
        console.log("Mnemonic Seed Phrase:", wallet.seedPhrase);
        console.log("Master Public Key (Ed25519 Base58):", wallet.publicKeyBase58Nacl);
        console.log("Master Secret Key (Ed25519 Base58):", wallet.secretKeyBase58Nacl);
        // Note: For security, you would typically not log or expose the secret key in a real application.
        // The privateKeyNacl (Uint8Array) is what you'd use internally for signing.

        console.log("\n--- Signing a Message ---");
        const messageToSign = "This is a test message for digital signature.";
        console.log("Message:", messageToSign);

        // Sign the message using the Ed25519 secret key (privateKeyNacl is a Uint8Array)
        const signature = KeyGen.signMessage(messageToSign, wallet.privateKeyNacl);
        console.log("Generated Signature (Base58):", signature);

        console.log("\n--- Verifying the Signature ---");
        // Verify the signature using the original message, the generated signature, and the Ed25519 public key
        const isValid = KeyGen.verifySignature(messageToSign, signature, wallet.publicKeyBase58Nacl);

        console.log("Signature Valid?", isValid);

        if (isValid) {
            console.log("🥳 Signature successfully verified!");
        } else {
            console.error("😭 Signature verification failed!");
        }

        // Example of deriving a child key and signing with it
        console.log("\n--- Deriving a Child Key and Signing ---");
        const childKey = KeyGen.deriveChildKey(wallet, 0); // Derive the first non-hardened child
        console.log("Child Key Public Key (Ed25519 Base58):", childKey.publicKeyBase58Nacl);

        const childMessage = "Message signed by a child key.";
        const childSignature = KeyGen.signMessage(childMessage, childKey.privateKeyNacl);
        console.log("Child Signature (Base58):", childSignature);

        const isChildSignatureValid = KeyGen.verifySignature(childMessage, childSignature, childKey.publicKeyBase58Nacl);
        console.log("Child Signature Valid?", isChildSignatureValid);

    } catch (error) {
        console.error("An error occurred:", error);
    }
}

runExample();