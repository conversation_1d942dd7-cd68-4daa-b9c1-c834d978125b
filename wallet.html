<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
            padding-top: 50px; 
            padding-bottom: 80px; /* Space for the fixed footer */
        }

        .gradient-bg {
            background: linear-gradient(135deg, #3C5B97 0%, #1A3E7D 100%); 
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 200px;
            z-index: -1;
        }

        .fixed-header-blur {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0);
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 20;
            transition: background-color 0.3s ease, backdrop-filter 0.3s ease;
        }

        /* Class for when header is scrolled */
        .fixed-header-blur.scrolled {
            background: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .main-content {
            flex-grow: 1;
            position: relative;
            z-index: 1;
            padding-left: 1.5rem;
            padding-right: 1.5rem;
        }

        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .animate-pulse-slow {
            animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        .tab-active {
            color: #3b82f6;
            border-bottom: 2px solid #3b82f6;
        }
        
        .tab-inactive {
            color: #9ca3af;
            border-bottom: 2px solid transparent;
        }
    </style>
</head>
<body class="bg-gray-900 text-white">

    <div class="gradient-bg">
        <div class="absolute top-0 left-0 w-full h-full opacity-20">
            <div class="absolute top-4 left-4 w-32 h-32 bg-white rounded-full opacity-10"></div>
            <div class="absolute bottom-4 right-4 w-48 h-48 bg-white rounded-full opacity-5"></div>
        </div>
    </div>

    <div id="fixedHeader" class="fixed-header-blur">
        <div class="flex justify-between items-center py-2 px-2.5"> 
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center">
                    <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full"></div>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="text-lg font-semibold">@CryptoUser</span>
                    <svg class="w-5 h-5 text-white opacity-70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
            </div>
            
            <div class="flex items-center space-x-4">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="w-8 h-8 bg-orange-500 rounded-full"></div>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="relative text-center mt-4 mb-8"> 
            <p class="text-sm opacity-80 mb-2">TOTAL BALANCE</p>
            <h2 class="text-4xl font-bold mb-2">$24,967.84</h2>
        </div>

        <div class="-mt-6 relative z-10">
            <div class="glass-effect rounded-2xl p-4">
                <div class="grid grid-cols-4 gap-4">
                    <button class="flex flex-col items-center space-y-2 p-3 rounded-xl hover:bg-white hover:bg-opacity-10 transition-all">
                        <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                        </div>
                        <span class="text-xs font-medium">Buy</span>
                    </button>
                    
                    <button class="flex flex-col items-center space-y-2 p-3 rounded-xl hover:bg-white hover:bg-opacity-10 transition-all">
                        <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                            </svg>
                        </div>
                        <span class="text-xs font-medium">Send</span>
                    </button>
                    
                    <button class="flex flex-col items-center space-y-2 p-3 rounded-xl hover:bg-white hover:bg-opacity-10 transition-all">
                        <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 13l-5 5m0 0l-5-5m5 5V6"></path>
                            </svg>
                        </div>
                        <span class="text-xs font-medium">Receive</span>
                    </button>
                    
                    <button class="flex flex-col items-center space-y-2 p-3 rounded-xl hover:bg-white hover:bg-opacity-10 transition-all">
                        <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                            </svg>
                        </div>
                        <span class="text-xs font-medium">Swap</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="mt-8">
            <div class="flex space-x-8 mb-6">
                <button id="tokensTab" class="tab-active py-3 px-1 font-medium text-sm transition-colors duration-200 border-b-2">
                    Tokens
                </button>
                <button id="nftsTab" class="tab-inactive py-3 px-1 font-medium text-sm transition-colors duration-200 border-b-2">
                    NFTs
                </button>
            </div>
            
            <div id="tokensContent" class="space-y-4">
                <div class="bg-gray-800 rounded-2xl p-4 card-hover">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold text-lg">₿</span>
                            </div>
                            <div>
                                <h4 class="font-semibold">Bitcoin</h4>
                                <p class="text-gray-400 text-sm">$45,126.30</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold">$12,847.30</p>
                            <p class="text-green-400 text-sm">0.2847 BTC</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800 rounded-2xl p-4 card-hover">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold text-lg">Ξ</span>
                            </div>
                            <div>
                                <h4 class="font-semibold">Ethereum</h4>
                                <p class="text-gray-400 text-sm">$2,387.50</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold">$8,234.75</p>
                            <p class="text-green-400 text-sm">3.45 ETH</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800 rounded-2xl p-4 card-hover">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold text-lg">◎</span>
                            </div>
                            <div>
                                <h4 class="font-semibold">Solana</h4>
                                <p class="text-gray-400 text-sm">$31.92</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold">$2,847.92</p>
                            <p class="text-green-400 text-sm">89.21 SOL</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800 rounded-2xl p-4 card-hover">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold text-lg">₳</span>
                            </div>
                            <div>
                                <h4 class="font-semibold">Cardano</h4>
                                <p class="text-gray-400 text-sm">$0.481</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold">$1,037.87</p>
                            <p class="text-green-400 text-sm">2,156 ADA</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div id="nftsContent" class="space-y-4 hidden">
                <div class="bg-gray-800 rounded-2xl p-4 card-hover">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-600 to-pink-600 rounded-lg flex items-center justify-center">
                                <span class="text-white font-bold text-sm">🖼️</span>
                            </div>
                            <div>
                                <h4 class="font-semibold">CryptoPunk #1234</h4>
                                <p class="text-gray-400 text-sm">Last sale: 12.5 ETH</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold">$29,875.00</p>
                            <p class="text-gray-400 text-sm">Floor: 11.2 ETH</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800 rounded-2xl p-4 card-hover">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-lg flex items-center justify-center">
                                <span class="text-white font-bold text-sm">🎨</span>
                            </div>
                            <div>
                                <h4 class="font-semibold">Bored Ape #5678</h4>
                                <p class="text-gray-400 text-sm">Last sale: 25.0 ETH</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold">$59,687.50</p>
                            <p class="text-gray-400 text-sm">Floor: 23.8 ETH</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gray-800 rounded-2xl p-4 card-hover">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                                <span class="text-white font-bold text-sm">🚀</span>
                            </div>
                            <div>
                                <h4 class="font-semibold">Space Monkey #999</h4>
                                <p class="text-gray-400 text-sm">Last sale: 3.2 ETH</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold">$7,640.00</p>
                            <p class="text-gray-400 text-sm">Floor: 2.8 ETH</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-8 mb-24"> 
            <h3 class="text-xl font-semibold mb-6">Recent Activity</h3>
            
            <div class="space-y-4">
                <div class="bg-gray-800 rounded-xl p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-green-500 bg-opacity-20 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 13l-5 5m0 0l-5-5m5 5V6"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium">Received Bitcoin</p>
                            <p class="text-gray-400 text-sm">2 hours ago</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-green-400">+0.0024 BTC</p>
                        <p class="text-gray-400 text-sm">$108.42</p>
                    </div>
                </div>
                
                <div class="bg-gray-800 rounded-xl p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-orange-500 bg-opacity-20 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium">Swapped ETH → SOL</p>
                            <p class="text-gray-400 text-sm">1 day ago</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold">-0.5 ETH</p>
                        <p class="text-gray-400 text-sm">$1,247.50</p>
                    </div>
                </div>
                
                <div class="bg-gray-800 rounded-xl p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-blue-500 bg-opacity-20 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium">Sent Ethereum</p>
                            <p class="text-gray-400 text-sm">3 days ago</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-red-400">-1.2 ETH</p>
                        <p class="text-gray-400 text-sm">$2,865.00</p>
                    </div>
                </div>
                
                <div class="bg-gray-800 rounded-xl p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-purple-500 bg-opacity-20 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium">Bought Bitcoin</p>
                            <p class="text-gray-400 text-sm">1 week ago</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-green-400">+0.1 BTC</p>
                        <p class="text-gray-400 text-sm">$4,512.60</p>
                    </div>
                </div>
                
                <div class="bg-gray-800 rounded-xl p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-green-500 bg-opacity-20 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 13l-5 5m0 0l-5-5m5 5V6"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium">Received Cardano</p>
                            <p class="text-gray-400 text-sm">2 weeks ago</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-green-400">+500 ADA</p>
                        <p class="text-gray-400 text-sm">$240.50</p>
                    </div>
                </div>
            </div>
        </div>
    </div> 

    <div class="fixed bottom-0 left-0 right-0 bg-gray-800 border-t border-gray-700 z-20">
        <div class="flex justify-around py-4">
            <button class="flex flex-col items-center space-y-1 text-blue-400">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                </svg>
                <span class="text-xs">Home</span>
            </button>
            
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <span class="text-xs">Portfolio</span>
            </button>
            
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
                <span class="text-xs">Markets</span>
            </button>
            
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-xs">Activity</span>
            </button>
            
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-xs">Profile</span>
            </button>
        </div>
    </div>

    <script>
        // Tab functionality
        const tokensTab = document.getElementById('tokensTab');
        const nftsTab = document.getElementById('nftsTab');
        const tokensContent = document.getElementById('tokensContent');
        const nftsContent = document.getElementById('nftsContent');

        function showTokens() {
            tokensTab.classList.add('tab-active');
            tokensTab.classList.remove('tab-inactive');
            nftsTab.classList.add('tab-inactive');
            nftsTab.classList.remove('tab-active');
            tokensContent.classList.remove('hidden');
            nftsContent.classList.add('hidden');
        }

        function showNFTs() {
            nftsTab.classList.add('tab-active');
            nftsTab.classList.remove('tab-inactive');
            tokensTab.classList.add('tab-inactive');
            tokensTab.classList.remove('tab-active');
            nftsContent.classList.remove('hidden');
            tokensContent.classList.add('hidden');
        }

        tokensTab.addEventListener('click', showTokens);
        nftsTab.addEventListener('click', showNFTs);

        // Scroll functionality for header blur
        const fixedHeader = document.getElementById('fixedHeader');
        const scrollThreshold = 50; // Smaller threshold for earlier blur

        function handleScroll() {
            if (window.scrollY > scrollThreshold) {
                fixedHeader.classList.add('scrolled');
            } else {
                fixedHeader.classList.remove('scrolled');
            }
        }

        window.addEventListener('scroll', handleScroll);

        // Initial check on load
        handleScroll();
    </script>
</body>
</html>