<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALT Wallet - Create</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            box-sizing: border-box;
        }
        /* Custom dark blue for the VALT theme */
        .bg-valt-dark-blue {
            background-color: #1A3E7D;
        }
        .hover\:bg-valt-dark-blue-darker:hover {
            background-color: #153466; /* A slightly darker shade for hover effect */
        }
        .focus\:ring-valt-dark-blue {
            --tw-ring-color: #1A3E7D;
        }
    </style>
</head>
<body class="flex flex-col items-center bg-gray-900 h-screen text-white">
    <div class="p-8 w-full max-w-full text-center flex-grow flex flex-col justify-between items-center relative">
        <div class="absolute top-4 left-4">
            <a href="setup.html" class="flex items-center text-gray-300 hover:text-white transition duration-200">
                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back
            </a>
        </div>

        <div class="flex flex-col items-center justify-center flex-grow">
            <div class="mb-8 p-4 bg-valt-dark-blue rounded-full">
                <svg class="h-16 w-16 text-white mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
            </div>

            <h1 class="text-3xl font-semibold mb-2">Create new wallet</h1>
            <p class="text-gray-300 text-base mb-8">Your secret recovery phrase gives you full control over your assets. Save it securely.</p>

            <div class="flex flex-col gap-4 w-full max-w-sm mb-8">
                <a href="phrase.html" class="w-full">
                    <button class="bg-valt-dark-blue text-white p-3 rounded-lg font-medium hover:bg-valt-dark-blue-darker focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:ring-opacity-50 transition duration-200 w-full">
                        Continue with Recovery Phrase
                    </button>
                </a>
            </div>
        </div>
    </div>
</body>
</html>