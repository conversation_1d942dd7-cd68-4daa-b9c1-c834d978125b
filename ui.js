import { KeyGen } from './KeyGen.js';

// Get DOM elements
const generateBtn = document.getElementById('generate-wallet-btn');
const walletDetailsContainer = document.getElementById('wallet-details');
const seedPhraseEl = document.getElementById('seed-phrase');
const xpubEl = document.getElementById('xpub');
const publicKeyEl = document.getElementById('public-key');
const secretKeyEl = document.getElementById('secret-key');

// Add event listener to the button
generateBtn.addEventListener('click', async () => {
    // Disable button and show loading state
    generateBtn.disabled = true;
    generateBtn.textContent = 'Generating...';

    try {
        // Generate the wallet
        const wallet = await KeyGen.generateHDWallet();

        // Get the extended public key
        const xpub = KeyGen.getExtendedPublicKey(wallet);

        // Populate the UI with the generated data
        seedPhraseEl.textContent = wallet.seedPhrase;
        xpubEl.textContent = xpub;
        publicKeyEl.textContent = wallet.publicKeyBase58Nacl;
        secretKeyEl.textContent = wallet.secretKeyBase58Nacl;

        // Show the details container
        walletDetailsContainer.classList.remove('hidden');

    } catch (error) {
        console.error("Failed to generate wallet:", error);
        alert("An error occurred while generating the wallet. Check the console for details.");
    } finally {
        // Re-enable the button
        generateBtn.disabled = false;
        generateBtn.textContent = 'Generate New Wallet';
    }
});