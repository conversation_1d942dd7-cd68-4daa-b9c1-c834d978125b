<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transaction History - VALT Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            box-sizing: border-box;
        }
        /* Custom scrollbar for webkit browsers for a cleaner look */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #2D3748; /* bg-gray-700 */
        }
        ::-webkit-scrollbar-thumb {
            background: #4A5568; /* bg-gray-600 */
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #606F7B; /* darker gray */
        }
    </style>
</head>
<body class="flex flex-col bg-gray-900 min-h-screen">

    <div class="flex items-center justify-between p-4 bg-gray-900 z-10 sticky top-0">
        <a href="#" class="flex items-center text-gray-300 hover:text-white transition duration-200">
            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            <span class="sr-only">Back</span> </a>
        <h1 class="text-white text-xl font-semibold">Transaction History</h1>
        <button class="text-gray-300 hover:text-white transition duration-200">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
            </svg>
            <span class="sr-only">Filter</span> </button>
    </div>

    <div class="flex justify-around p-4 bg-gray-800 rounded-lg mx-4 mt-2 mb-6">
        <button class="filter-btn px-4 py-2 rounded-lg text-gray-300 font-medium hover:bg-gray-700 active-filter bg-blue-600 text-white">All</button>
        <button class="filter-btn px-4 py-2 rounded-lg text-gray-300 font-medium hover:bg-gray-700">Sent</button>
        <button class="filter-btn px-4 py-2 rounded-lg text-gray-300 font-medium hover:bg-gray-700">Received</button>
        <button class="filter-btn px-4 py-2 rounded-lg text-gray-300 font-medium hover:bg-gray-700">Pending</button>
    </div>

    <div id="transaction-list" class="flex-grow px-4 pb-8">
        <div class="transaction-group" data-group="today">
            <h2 class="text-gray-400 text-sm font-semibold uppercase mt-6 mb-3">Today</h2>
            <div class="transaction-item bg-gray-800 p-4 rounded-xl flex items-center justify-between mb-3 cursor-pointer hover:bg-gray-700 transition duration-150">
                <div class="flex items-center">
                    <div class="bg-green-600 p-2 rounded-full mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-white font-medium">Received Bitcoin</p>
                        <p class="text-gray-400 text-sm">2 hours ago</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-green-500 font-medium">+0.0024 BTC</p>
                    <p class="text-gray-400 text-sm">$108.42</p>
                </div>
            </div>
            <div class="transaction-item bg-gray-800 p-4 rounded-xl flex items-center justify-between mb-3 cursor-pointer hover:bg-gray-700 transition duration-150">
                <div class="flex items-center">
                    <div class="bg-orange-500 p-2 rounded-full mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-white font-medium">Swapped ETH → SOL</p>
                        <p class="text-gray-400 text-sm">4 hours ago</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-red-500 font-medium">-0.5 ETH</p>
                    <p class="text-gray-400 text-sm">$1,247.50</p>
                </div>
            </div>
        </div>

        <div class="transaction-group" data-group="yesterday">
            <h2 class="text-gray-400 text-sm font-semibold uppercase mt-6 mb-3">Yesterday</h2>
            <div class="transaction-item bg-gray-800 p-4 rounded-xl flex items-center justify-between mb-3 cursor-pointer hover:bg-gray-700 transition duration-150">
                <div class="flex items-center">
                    <div class="bg-blue-600 p-2 rounded-full mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-white font-medium">Sent Ethereum</p>
                        <p class="text-gray-400 text-sm">3 days ago</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-red-500 font-medium">-1.2 ETH</p>
                    <p class="text-gray-400 text-sm">$2,865.00</p>
                </div>
            </div>
            <div class="transaction-item bg-gray-800 p-4 rounded-xl flex items-center justify-between mb-3 cursor-pointer hover:bg-gray-700 transition duration-150">
                <div class="flex items-center">
                    <div class="bg-purple-600 p-2 rounded-full mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 3h3m-6 3h6"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="text-white font-medium">Bought Bitcoin</p>
                        <p class="text-gray-400 text-sm">5 days ago</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-green-500 font-medium">+0.1 BTC</p>
                    <p class="text-gray-400 text-sm">$4,500.00</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Placeholder for transaction data (replace with real data fetched from an API)
        const allTransactions = [
            {
                id: 'txn1',
                type: 'received',
                asset: 'Bitcoin',
                amount: 0.0024,
                fiatValue: 108.42,
                time: '2 hours ago',
                dateGroup: 'Today',
                status: 'completed'
            },
            {
                id: 'txn2',
                type: 'swapped',
                asset: 'ETH',
                amount: -0.5,
                fiatValue: 1247.50,
                time: '4 hours ago',
                dateGroup: 'Today',
                status: 'completed',
                toAsset: 'SOL'
            },
            {
                id: 'txn3',
                type: 'sent',
                asset: 'Ethereum',
                amount: -1.2,
                fiatValue: 2865.00,
                time: '3 days ago',
                dateGroup: 'Yesterday',
                status: 'completed'
            },
            {
                id: 'txn4',
                type: 'bought',
                asset: 'Bitcoin',
                amount: 0.1,
                fiatValue: 4500.00,
                time: '5 days ago',
                dateGroup: 'Yesterday',
                status: 'completed'
            },
            {
                id: 'txn5',
                type: 'sent',
                asset: 'ETH',
                amount: -0.1,
                fiatValue: 200.00,
                time: '1 hour ago',
                dateGroup: 'Today',
                status: 'pending' // Example of a pending transaction
            }
        ];

        const transactionListContainer = document.getElementById('transaction-list');
        const filterButtons = document.querySelectorAll('.filter-btn');

        function getTransactionIcon(type) {
            switch (type) {
                case 'received': return `<div class="bg-green-600 p-2 rounded-full mr-3"><svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path></svg></div>`;
                case 'sent': return `<div class="bg-blue-600 p-2 rounded-full mr-3"><svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path></svg></div>`;
                case 'swapped': return `<div class="bg-orange-500 p-2 rounded-full mr-3"><svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path></svg></div>`;
                case 'bought': return `<div class="bg-purple-600 p-2 rounded-full mr-3"><svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 3h3m-6 3h6"></path></svg></div>`;
                default: return `<div class="bg-gray-600 p-2 rounded-full mr-3"><svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg></div>`;
            }
        }

        function getAmountClass(amount) {
            return amount > 0 ? 'text-green-500' : 'text-red-500';
        }

        function renderTransactions(filter = 'All') {
            transactionListContainer.innerHTML = ''; // Clear current list

            const groupedTransactions = {};
            const filteredTransactions = allTransactions.filter(txn => {
                if (filter === 'All') return true;
                if (filter === 'Pending' && txn.status === 'pending') return true;
                if (filter === 'Sent' && txn.type === 'sent') return true;
                if (filter === 'Received' && txn.type === 'received') return true;
                // Add more conditions for other filters if needed
                return false;
            });

            filteredTransactions.forEach(txn => {
                if (!groupedTransactions[txn.dateGroup]) {
                    groupedTransactions[txn.dateGroup] = [];
                }
                groupedTransactions[txn.dateGroup].push(txn);
            });

            for (const groupName in groupedTransactions) {
                const groupHeader = document.createElement('h2');
                groupHeader.className = 'text-gray-400 text-sm font-semibold uppercase mt-6 mb-3';
                groupHeader.textContent = groupName;
                transactionListContainer.appendChild(groupHeader);

                groupedTransactions[groupName].forEach(txn => {
                    const transactionItem = document.createElement('div');
                    transactionItem.className = 'transaction-item bg-gray-800 p-4 rounded-xl flex items-center justify-between mb-3 cursor-pointer hover:bg-gray-700 transition duration-150';
                    transactionItem.innerHTML = `
                        <div class="flex items-center">
                            ${getTransactionIcon(txn.type)}
                            <div>
                                <p class="text-white font-medium">${txn.type.charAt(0).toUpperCase() + txn.type.slice(1)} ${txn.asset} ${txn.toAsset ? '→ ' + txn.toAsset : ''}</p>
                                <p class="text-gray-400 text-sm">${txn.time}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="${getAmountClass(txn.amount)} font-medium">${txn.amount > 0 ? '+' : ''}${txn.amount} ${txn.asset}</p>
                            <p class="text-gray-400 text-sm">$${txn.fiatValue.toFixed(2)}</p>
                        </div>
                    `;
                    // Add event listener to each transaction item to potentially navigate to a detail page
                    transactionItem.addEventListener('click', () => {
                        alert('Navigating to transaction details for: ' + txn.id);
                        // In a real app, you would navigate to a new page, e.g.:
                        // window.location.href = `/transaction-details?id=${txn.id}`;
                    });
                    transactionListContainer.appendChild(transactionItem);
                });
            }

            // If no transactions for the current filter, display a message
            if (filteredTransactions.length === 0) {
                transactionListContainer.innerHTML = `
                    <div class="text-center text-gray-400 mt-10">
                        No transactions found for this filter.
                    </div>
                `;
            }
        }

        // Event listeners for filter buttons
        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                filterButtons.forEach(btn => btn.classList.remove('active-filter', 'bg-blue-600', 'text-white'));
                button.classList.add('active-filter', 'bg-blue-600', 'text-white');
                renderTransactions(button.textContent);
            });
        });

        // Initial render on page load
        document.addEventListener('DOMContentLoaded', () => {
            renderTransactions('All');
        });

    </script>
</body>
</html>