<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wallet Generator</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-gray-100 font-sans">
    <div class="container mx-auto p-8">
        <h1 class="text-4xl font-bold mb-6 text-center text-cyan-400">Wallet Generator</h1>

        <div class="text-center mb-8">
            <button id="generate-wallet-btn" class="bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-3 px-6 rounded-lg shadow-lg transition duration-300 ease-in-out transform hover:scale-105 disabled:bg-gray-600 disabled:cursor-not-allowed">
                Generate New Wallet
            </button>
        </div>

        <div id="wallet-details" class="hidden bg-gray-800 rounded-lg shadow-xl p-6 space-y-4">
            <div>
                <h2 class="text-2xl font-semibold text-cyan-300 mb-2">Seed Phrase</h2>
                <p id="seed-phrase" class="bg-gray-700 p-3 rounded font-mono text-lg text-yellow-300 break-words"></p>
                <p class="text-sm text-gray-400 mt-2">⚠️ Store this phrase securely. It's the only way to recover your wallet.</p>
            </div>
            <div>
                <h2 class="text-2xl font-semibold text-cyan-300 mb-2">Extended Public Key (xpub)</h2>
                <p id="xpub" class="bg-gray-700 p-3 rounded font-mono text-sm break-words"></p>
            </div>
            <div>
                <h2 class="text-2xl font-semibold text-cyan-300 mb-2">Ed25519 Public Key (Base58)</h2>
                <p id="public-key" class="bg-gray-700 p-3 rounded font-mono text-sm break-words"></p>
            </div>
            <div>
                <h2 class="text-2xl font-semibold text-cyan-300 mb-2">Ed25519 Secret Key (Base58)</h2>
                <p id="secret-key" class="bg-gray-700 p-3 rounded font-mono text-sm break-words"></p>
                <p class="text-sm text-gray-400 mt-2">⚠️ Never share your secret key.</p>
            </div>
        </div>
    </div>

    <script type="module" src="ui.js"></script>
</body>
</html>