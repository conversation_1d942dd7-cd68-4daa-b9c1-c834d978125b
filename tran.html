<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALT Wallet - Transaction History</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #312e81 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .transaction-item {
            transition: all 0.2s ease;
        }
        .transaction-item:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        .status-completed {
            background-color: #d1fae5;
            color: #065f46;
        }
        .status-failed {
            background-color: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <div class="gradient-bg text-white">
        <div class="max-w-md mx-auto">
            <div class="flex items-center justify-between p-4">
                <button class="flex items-center space-x-2 text-white" onclick="goBack()">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    <span class="text-sm">Back</span>
                </button>
                <button class="p-2" onclick="showFilter()">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"></path>
                    </svg>
                </button>
            </div>
            <div class="px-4 pb-6">
                <h1 class="text-2xl font-bold mb-2">Transaction History</h1>
                <p class="text-blue-100 text-sm">Track all your wallet activity</p>
            </div>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="max-w-md mx-auto bg-white border-b">
        <div class="flex">
            <button class="flex-1 py-3 px-4 text-sm font-medium text-blue-600 border-b-2 border-blue-600 bg-blue-50" onclick="filterTransactions('all')">
                All
            </button>
            <button class="flex-1 py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700" onclick="filterTransactions('sent')">
                Sent
            </button>
            <button class="flex-1 py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700" onclick="filterTransactions('received')">
                Received
            </button>
            <button class="flex-1 py-3 px-4 text-sm font-medium text-gray-500 hover:text-gray-700" onclick="filterTransactions('pending')">
                Pending
            </button>
        </div>
    </div>

    <!-- Transaction List -->
    <div class="max-w-md mx-auto bg-white">
        <div class="px-4 py-2">
            <p class="text-xs text-gray-500 font-medium">TODAY</p>
        </div>
        
        <!-- Transaction Items -->
        <div class="transaction-item px-4 py-3 border-b border-gray-100 cursor-pointer" onclick="showTransactionDetails('tx1')">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">Received Bitcoin</p>
                        <p class="text-sm text-gray-500">From: **********************************</p>
                        <div class="flex items-center space-x-2 mt-1">
                            <span class="status-completed text-xs px-2 py-1 rounded-full font-medium">Completed</span>
                            <span class="text-xs text-gray-400">2:34 PM</span>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-bold text-green-600">+0.0542 BTC</p>
                    <p class="text-sm text-gray-500">+$1,847.20</p>
                </div>
            </div>
        </div>

        <div class="transaction-item px-4 py-3 border-b border-gray-100 cursor-pointer" onclick="showTransactionDetails('tx2')">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">Sent Ethereum</p>
                        <p class="text-sm text-gray-500">To: 0x742d35Cc6634C0532925a3b8D400</p>
                        <div class="flex items-center space-x-2 mt-1">
                            <span class="status-pending text-xs px-2 py-1 rounded-full font-medium">Pending</span>
                            <span class="text-xs text-gray-400">1:15 PM</span>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-bold text-red-600">-2.5 ETH</p>
                    <p class="text-sm text-gray-500">-$8,250.00</p>
                </div>
            </div>
        </div>

        <div class="px-4 py-2 mt-4">
            <p class="text-xs text-gray-500 font-medium">YESTERDAY</p>
        </div>

        <div class="transaction-item px-4 py-3 border-b border-gray-100 cursor-pointer" onclick="showTransactionDetails('tx3')">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">Swapped BTC → ETH</p>
                        <p class="text-sm text-gray-500">Via UniSwap</p>
                        <div class="flex items-center space-x-2 mt-1">
                            <span class="status-completed text-xs px-2 py-1 rounded-full font-medium">Completed</span>
                            <span class="text-xs text-gray-400">4:22 PM</span>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-bold text-gray-900">0.02 BTC → 0.8 ETH</p>
                    <p class="text-sm text-gray-500">$680.00</p>
                </div>
            </div>
        </div>

        <div class="transaction-item px-4 py-3 border-b border-gray-100 cursor-pointer" onclick="showTransactionDetails('tx4')">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">Sent Bitcoin</p>
                        <p class="text-sm text-gray-500">To: **********************************</p>
                        <div class="flex items-center space-x-2 mt-1">
                            <span class="status-failed text-xs px-2 py-1 rounded-full font-medium">Failed</span>
                            <span class="text-xs text-gray-400">2:10 PM</span>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-bold text-red-600">-0.1 BTC</p>
                    <p class="text-sm text-gray-500">-$3,400.00</p>
                </div>
            </div>
        </div>

        <div class="px-4 py-2 mt-4">
            <p class="text-xs text-gray-500 font-medium">LAST WEEK</p>
        </div>

        <div class="transaction-item px-4 py-3 border-b border-gray-100 cursor-pointer" onclick="showTransactionDetails('tx5')">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">Received Ethereum</p>
                        <p class="text-sm text-gray-500">From: 0x8ba1f109551bD432803012645Hac</p>
                        <div class="flex items-center space-x-2 mt-1">
                            <span class="status-completed text-xs px-2 py-1 rounded-full font-medium">Completed</span>
                            <span class="text-xs text-gray-400">Mar 1</span>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-bold text-green-600">+5.2 ETH</p>
                    <p class="text-sm text-gray-500">+$17,160.00</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Load More Button -->
    <div class="max-w-md mx-auto bg-white p-4">
        <button class="w-full py-3 px-4 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors" onclick="loadMoreTransactions()">
            Load More Transactions
        </button>
    </div>

    <!-- Transaction Details Modal -->
    <div id="transactionModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-end justify-center min-h-screen">
            <div class="bg-white rounded-t-xl w-full max-w-md max-h-96 overflow-y-auto">
                <div class="sticky top-0 bg-white border-b px-4 py-3">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold">Transaction Details</h3>
                        <button onclick="closeModal()" class="p-1 hover:bg-gray-100 rounded">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="p-4 space-y-4">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <p class="text-2xl font-bold text-green-600">+0.0542 BTC</p>
                        <p class="text-gray-500">+$1,847.20</p>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-500">Status</span>
                            <span class="status-completed text-xs px-2 py-1 rounded-full font-medium">Completed</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Date</span>
                            <span class="text-gray-900">Mar 6, 2024 at 2:34 PM</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Transaction ID</span>
                            <span class="text-gray-900 text-sm">a1b2c3d4...e5f6</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">Network Fee</span>
                            <span class="text-gray-900">0.0001 BTC</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">From</span>
                            <span class="text-gray-900 text-sm">**********************************</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">To</span>
                            <span class="text-gray-900 text-sm">Your Wallet</span>
                        </div>
                    </div>
                    
                    <button class="w-full py-3 px-4 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors">
                        View on Blockchain
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            history.back();
        }

        function showFilter() {
            alert('Filter options would appear here');
        }

        function filterTransactions(type) {
            // Remove active state from all buttons
            document.querySelectorAll('.flex button').forEach(btn => {
                btn.classList.remove('text-blue-600', 'border-b-2', 'border-blue-600', 'bg-blue-50');
                btn.classList.add('text-gray-500', 'hover:text-gray-700');
            });
            
            // Add active state to clicked button
            event.target.classList.remove('text-gray-500', 'hover:text-gray-700');
            event.target.classList.add('text-blue-600', 'border-b-2', 'border-blue-600', 'bg-blue-50');
            
            // Filter logic would go here
            console.log('Filtering by:', type);
        }

        function showTransactionDetails(txId) {
            document.getElementById('transactionModal').classList.remove('hidden');
            // Load specific transaction details based on txId
        }

        function closeModal() {
            document.getElementById('transactionModal').classList.add('hidden');
        }

        function loadMoreTransactions() {
            // Load more transactions logic
            console.log('Loading more transactions...');
        }

        // Close modal when clicking outside
        document.getElementById('transactionModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>