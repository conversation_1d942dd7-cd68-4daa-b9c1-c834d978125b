<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALT Wallet - Create Username</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            box-sizing: border-box;
        }
        /* Custom dark blue for the VALT theme */
        .bg-valt-dark-blue {
            background-color: #1A3E7D;
        }
        .hover\:bg-valt-dark-blue-darker:hover {
            background-color: #153466; /* A slightly darker shade for hover effect */
        }
        .focus\:ring-valt-dark-blue {
            --tw-ring-color: #1A3E7D;
        }
    </style>
</head>
<body class="flex flex-col items-center bg-gray-900 h-screen text-white">
    <div class="p-8 w-full max-w-full text-center flex-grow flex flex-col justify-between items-center relative">
        <div class="absolute top-4 left-4">
            <a href="#" class="flex items-center text-gray-300 hover:text-white transition duration-200">
                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back
            </a>
        </div>

        <div class="flex flex-col items-center justify-center flex-grow">
            <div class="mb-8 p-4 bg-valt-dark-blue rounded-full">
                <svg class="h-16 w-16 text-white mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>

            <h1 class="text-3xl font-semibold mb-2">Create Username</h1>
            <p class="text-gray-300 text-base mb-8">Personalize your account with a unique name. Usernames can be changed later on.</p>

            <div class="w-full max-w-sm mb-8">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span class="text-gray-400 text-lg">@</span>
                    </div>
                    <input
                        type="text"
                        id="username"
                        placeholder="Enter username"
                        class="w-full pl-8 pr-10 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition duration-200"
                        value="Greenman22"
                    >
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <button class="text-gray-400 hover:text-white">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="flex items-center mt-2">
                    <svg class="h-4 w-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span class="text-green-400 text-sm">Username available</span>
                </div>
            </div>

            <div class="flex flex-col gap-4 w-full max-w-sm">
                <button class="bg-valt-dark-blue text-white p-3 rounded-lg font-medium hover:bg-valt-dark-blue-darker focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:ring-opacity-50 transition duration-200">
                    Continue
                </button>
            </div>
        </div>
    </div>

    <script>
        // Username validation and clear functionality
        const usernameInput = document.getElementById('username');
        const clearButton = usernameInput.nextElementSibling.querySelector('button');
        const statusMessage = document.querySelector('.text-green-400').parentElement;

        clearButton.addEventListener('click', () => {
            usernameInput.value = '';
            usernameInput.focus();
            updateStatus('', false);
        });

        usernameInput.addEventListener('input', (e) => {
            const value = e.target.value.trim();
            if (value.length === 0) {
                updateStatus('', false);
            } else if (value.length < 3) {
                updateStatus('Username must be at least 3 characters', false);
            } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
                updateStatus('Username can only contain letters, numbers, and underscores', false);
            } else {
                // Simulate availability check
                setTimeout(() => {
                    updateStatus('Username available', true);
                }, 300);
            }
        });

        function updateStatus(message, isAvailable) {
            const icon = statusMessage.querySelector('svg');
            const text = statusMessage.querySelector('span');

            if (message === '') {
                statusMessage.style.display = 'none';
            } else {
                statusMessage.style.display = 'flex';
                text.textContent = message;

                if (isAvailable) {
                    icon.classList.remove('text-red-400');
                    icon.classList.add('text-green-400');
                    text.classList.remove('text-red-400');
                    text.classList.add('text-green-400');
                    icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>';
                } else {
                    icon.classList.remove('text-green-400');
                    icon.classList.add('text-red-400');
                    text.classList.remove('text-green-400');
                    text.classList.add('text-red-400');
                    icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';
                }
            }
        }
    </script>
</body>
</html>