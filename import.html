<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import VALT Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            box-sizing: border-box;
        }
        /* Custom dark blue for the VALT theme */
        .bg-valt-dark-blue {
            background-color: #1A3E7D;
        }
        .hover\:bg-valt-dark-blue-darker:hover {
            background-color: #153466; /* A slightly darker shade for hover effect */
        }
        .focus\:ring-valt-dark-blue {
            --tw-ring-color: #1A3E7D;
        }
        /* Custom styles for the dark button background from the image */
        .btn-dark-bg {
            background-color: #2F3E5E; /* A shade close to the button background in the image */
            color: #FFFFFF;
        }
        .hover\:btn-dark-bg-hover:hover {
            background-color: #3B4D6F; /* Slightly lighter on hover for better interaction */
        }
        .text-dark-text-light {
            color: #D1D5DB; /* A lighter gray for secondary text, similar to the image */
        }
        .input-dark-border {
            border-color: #4A5568; /* Darker border for input fields */
        }
        .active-tab {
            border-bottom: 2px solid #1A3E7D; /* Highlight active tab with your custom blue */
            color: #1A3E7D;
        }
    </style>
</head>
<body class="bg-gray-900 text-white flex flex-col min-h-screen">
    <div class="p-4">
        <a href="setup.html" class="flex items-center text-gray-300 hover:text-white transition duration-200">
            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back
        </a>
    </div>

    <div class="flex-grow flex flex-col items-center justify-center p-4 pt-0 md:pt-4"> <div class="text-center max-w-md mx-auto w-full">

            <div class="w-24 h-24 mx-auto mb-6 bg-valt-dark-blue rounded-full flex items-center justify-center">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
            </div>

            <h1 class="text-3xl font-semibold mb-2">Import Your Wallet</h1>
            <p class="text-gray-400 text-md mb-8">
                Restore your wallet using your seed phrase, private key, or keystore file.
            </p>

            <div class="mb-8 border-b border-gray-700">
                <nav class="flex justify-center -mb-px" role="tablist">
                    <button id="tab-seed-phrase" class="py-3 px-6 text-lg font-medium text-gray-400 hover:text-white border-b-2 border-transparent hover:border-valt-dark-blue focus:outline-none transition-colors duration-200 active-tab" role="tab" aria-selected="true" aria-controls="panel-seed-phrase">
                        Seed Phrase
                    </button>
                    <button id="tab-private-key" class="py-3 px-6 text-lg font-medium text-gray-400 hover:text-white border-b-2 border-transparent hover:border-valt-dark-blue focus:outline-none transition-colors duration-200" role="tab" aria-selected="false" aria-controls="panel-private-key">
                        Private Key
                    </button>
                    <button id="tab-keystore" class="py-3 px-6 text-lg font-medium text-gray-400 hover:text-white border-b-2 border-transparent hover:border-valt-dark-blue focus:outline-none transition-colors duration-200" role="tab" aria-selected="false" aria-controls="panel-keystore">
                        Keystore JSON
                    </button>
                </nav>
            </div>

            <div id="tab-content">
                <div id="panel-seed-phrase" role="tabpanel" aria-labelledby="tab-seed-phrase" class="tab-panel active">
                    <form class="space-y-6">
                        <div>
                            <label for="seed-phrase" class="block text-left text-sm font-medium text-gray-300 mb-2">Enter your 12 or 24-word seed phrase:</label>
                            <textarea id="seed-phrase" rows="4" class="block w-full px-4 py-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition-colors duration-200 resize-none" placeholder="Enter your seed phrase here, separated by spaces..."></textarea>
                            <p class="text-sm text-gray-500 mt-2 text-left">Usually 12 or 24 words (e.g., word1 word2 ...).</p>
                        </div>

                        <div>
                            <label for="new-password-seed" class="block text-left text-sm font-medium text-gray-300 mb-2">New Password (optional):</label>
                            <input type="password" id="new-password-seed" class="block w-full px-4 py-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition-colors duration-200" placeholder="Create a new password for your wallet">
                        </div>
                        <div>
                            <label for="confirm-password-seed" class="block text-left text-sm font-medium text-gray-300 mb-2">Confirm New Password:</label>
                            <input type="password" id="confirm-password-seed" class="block w-full px-4 py-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition-colors duration-200" placeholder="Confirm your new password">
                        </div>

                        <button type="submit" class="w-full btn-dark-bg hover:btn-dark-bg-hover text-white font-semibold py-3 px-6 rounded-lg shadow-lg transition duration-300 ease-in-out transform hover:scale-105">
                            Import Seed Phrase
                        </button>
                    </form>
                </div>

                <div id="panel-private-key" role="tabpanel" aria-labelledby="tab-private-key" class="tab-panel hidden">
                    <form class="space-y-6">
                        <div>
                            <label for="private-key" class="block text-left text-sm font-medium text-gray-300 mb-2">Enter your private key:</label>
                            <input type="text" id="private-key" class="block w-full px-4 py-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition-colors duration-200" placeholder="0x...">
                            <p class="text-sm text-gray-500 mt-2 text-left">This is a long string of hexadecimal characters.</p>
                        </div>

                        <div>
                            <label for="new-password-pk" class="block text-left text-sm font-medium text-gray-300 mb-2">New Password (optional):</label>
                            <input type="password" id="new-password-pk" class="block w-full px-4 py-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition-colors duration-200" placeholder="Create a new password for your wallet">
                        </div>
                        <div>
                            <label for="confirm-password-pk" class="block text-left text-sm font-medium text-gray-300 mb-2">Confirm New Password:</label>
                            <input type="password" id="confirm-password-pk" class="block w-full px-4 py-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition-colors duration-200" placeholder="Confirm your new password">
                        </div>

                        <button type="submit" class="w-full btn-dark-bg hover:btn-dark-bg-hover text-white font-semibold py-3 px-6 rounded-lg shadow-lg transition duration-300 ease-in-out transform hover:scale-105">
                            Import Private Key
                        </button>
                    </form>
                </div>

                <div id="panel-keystore" role="tabpanel" aria-labelledby="tab-keystore" class="tab-panel hidden">
                    <form class="space-y-6">
                        <div>
                            <label for="keystore-file" class="block text-left text-sm font-medium text-gray-300 mb-2">Upload your Keystore JSON file:</label>
                            <input type="file" id="keystore-file" accept=".json" class="block w-full text-white bg-gray-700 py-2 px-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition-colors duration-200 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-valt-dark-blue file:text-white hover:file:bg-valt-dark-blue-darker cursor-pointer">
                            <p class="text-sm text-gray-500 mt-2 text-left">Select the `.json` file containing your wallet data.</p>
                        </div>

                        <div>
                            <label for="keystore-password" class="block text-left text-sm font-medium text-gray-300 mb-2">Keystore Password:</label>
                            <input type="password" id="keystore-password" class="block w-full px-4 py-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-valt-dark-blue focus:border-transparent transition-colors duration-200" placeholder="Password for your keystore file">
                        </div>

                        <button type="submit" class="w-full btn-dark-bg hover:btn-dark-bg-hover text-white font-semibold py-3 px-6 rounded-lg shadow-lg transition duration-300 ease-in-out transform hover:scale-105">
                            Import Keystore
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const tabs = document.querySelectorAll('[role="tab"]');
            const tabPanels = document.querySelectorAll('.tab-panel');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Deactivate all tabs
                    tabs.forEach(t => {
                        t.classList.remove('active-tab');
                        t.setAttribute('aria-selected', 'false');
                        t.classList.remove('text-valt-dark-blue'); // Remove color for inactive tabs
                        t.classList.add('text-gray-400'); // Add gray for inactive tabs
                    });

                    // Hide all tab panels
                    tabPanels.forEach(panel => panel.classList.add('hidden'));

                    // Activate the clicked tab
                    tab.classList.add('active-tab');
                    tab.setAttribute('aria-selected', 'true');
                    tab.classList.remove('text-gray-400'); // Remove gray
                    tab.classList.add('text-valt-dark-blue'); // Add blue for active tab

                    // Show the corresponding tab panel
                    const targetPanelId = tab.getAttribute('aria-controls');
                    document.getElementById(targetPanelId).classList.remove('hidden');
                });
            });

            // Set the first tab as active by default on load
            const initialTab = document.getElementById('tab-seed-phrase');
            if (initialTab) {
                initialTab.click(); // Simulate a click to activate the first tab and its content
            }
        });
    </script>
</body>
</html>