<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VALT Wallet - Recovery Phrase</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            box-sizing: border-box;
        }
        /* Custom blur class for more control if needed, though Tailwind's blur-sm is usually sufficient */
        .blur-text {
            filter: blur(4px);
            transition: filter 0.3s ease-in-out;
        }
        .blur-text.revealed {
            filter: blur(0);
        }
        /* Custom dark blue for the VALT theme */
        .bg-valt-dark-blue {
            background-color: #1A3E7D;
        }
        .hover\:bg-valt-dark-blue-darker:hover {
            background-color: #153466; /* A slightly darker shade for hover effect */
        }
        .focus\:ring-valt-dark-blue {
            --tw-ring-color: #1A3E7D;
        }
    </style>
</head>
<body class="flex flex-col bg-gray-900 h-screen relative">
    <div class="absolute top-0 left-0 z-10 p-4">
        <a href="#" class="flex items-center text-gray-300 hover:text-white transition duration-200">
            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back
        </a>
    </div>

    <div class="flex-grow flex flex-col justify-center items-center px-4 sm:px-8 md:px-12 lg:px-16 pt-16 pb-8">
        <div class="flex flex-col lg:flex-row items-center lg:items-start justify-center w-full max-w-6xl">
            <div class="flex flex-col items-center lg:items-start lg:w-1/2 mb-8 lg:mb-0 lg:pr-8 text-center lg:text-left w-full">
                <h1 class="text-white text-3xl font-semibold mb-2">Recovery Phrase</h1>
                <p class="text-gray-300 text-lg mb-8">Read the following, then save the phrase securely.</p>

                <ul class="space-y-4 text-gray-300 text-base w-full">
                    <li class="flex items-start">
                        <svg class="h-6 w-6 text-blue-400 mr-3 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10m-8-4l8 4m-8-4V7m8 4L4 7m8 4v10m-8-4l8 4m-8-4V7m8 4L4 7m8 4v10m-8-4l8 4m-8-4V7"></path>
                        </svg>
                        Your recovery phrase acts as a master password, giving you full control over your digital assets.
                    </li>
                    <li class="flex items-start">
                        <svg class="h-6 w-6 text-blue-400 mr-3 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        If you forget your password, you can use the recovery phrase to get back into your wallet.
                    </li>
                    <li class="flex items-start">
                        <svg class="h-6 w-6 text-blue-400 mr-3 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9.25 17M14.25 17L14.75 17M12 3a1 1 0 011 1v2a1 1 0 01-2 0V4a1 1 0 011-1zM12 18a1 1 0 011 1v2a1 1 0 01-2 0v-2a1 1 0 011-1zM4 12a1 1 0 011-1h2a1 1 0 010 2H5a1 1 0 01-1-1zM17 12a1 1 0 011-1h2a1 1 0 010 2h-2a1 1 0 01-1-1zM7 7a1 1 0 011-1h2a1 1 0 010 2H8a1 1 0 01-1-1zM14 7a1 1 0 011-1h2a1 1 0 010 2h-2a1 1 0 01-1-1zM7 14a1 1 0 011-1h2a1 1 0 010 2H8a1 1 0 01-1-1zM14 14a1 1 0 011-1h2a1 1 0 010 2h-2a1 1 0 01-1-1z"></path>
                        </svg>
                        It is important that you keep your recovery phrase confidential and never disclose it to anyone, including VALT.
                    </li>
                </ul>
            </div>

            <div class="flex flex-col items-center lg:items-start lg:w-1/2 mt-8 lg:mt-0 text-center lg:text-left w-full">
                <p class="text-gray-300 text-sm mb-4 flex items-center">
                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                    </svg>
                    Write this down or save somewhere safe
                </p>

                <div class="bg-gray-700 p-6 rounded-xl w-full mb-6 relative">
                    <div id="recovery-phrase-container" class="blur-text grid grid-cols-2 gap-4 text-white text-lg font-mono">
                        </div>
                    <div id="reveal-overlay" class="absolute inset-0 flex items-center justify-center bg-gray-700 bg-opacity-90 rounded-xl cursor-pointer" onclick="toggleRevealPhrase()">
                        <span class="text-blue-400 font-medium flex items-center">
                            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            Reveal Recovery Phrase
                        </span>
                    </div>
                </div>

                <button
                    class="bg-gray-700 text-white p-3 rounded-lg font-medium hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 transition duration-200 w-full mb-6 flex items-center justify-center"
                    onclick="copyToClipboard()"
                >
                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                    Copy to Clipboard
                </button>

                <div class="flex items-start w-full mb-8">
                    <input type="checkbox" id="understand-checkbox" class="form-checkbox h-5 w-5 text-blue-600 rounded border-gray-600 focus:ring-blue-500 bg-gray-700 mt-1" onchange="toggleContinueButton()">
                    <label for="understand-checkbox" class="ml-3 text-gray-300 text-sm text-left">
                        I understand that VALT cannot recover this Secret recovery phrase
                    </label>
                </div>

                <button
                    id="continue-button"
                    class="w-full bg-blue-600 text-white p-3 rounded-lg font-medium opacity-50 cursor-not-allowed"
                    disabled
                >
                    Continue
                </button>
            </div>
        </div>
    </div>

    <script type="module">
        import { KeyGen } from './KeyGen.js'; // Assuming KeyGen.js is in the same directory

        let generatedSeedPhrase = ''; // Global variable to store the generated phrase

        document.addEventListener('DOMContentLoaded', async () => {
            const phraseContainer = document.getElementById('recovery-phrase-container');
            const revealOverlay = document.getElementById('reveal-overlay'); // Get the overlay reference
            
            try {
                // Generate the wallet on page load
                const wallet = await KeyGen.generateHDWallet();
                generatedSeedPhrase = wallet.seedPhrase; // Store the generated phrase

                // Populate the UI with the generated seed phrase words
                const words = generatedSeedPhrase.split(' ');
                phraseContainer.innerHTML = words.map((word, index) => `
                    <div class="flex items-center">
                        <span class="text-gray-400 text-sm mr-2 w-5">${index + 1}.</span>
                        <span>${word}</span>
                    </div>
                `).join('');

            } catch (error) {
                console.error("Failed to generate wallet:", error);
                phraseContainer.innerHTML = `<p class="text-red-400">Error generating wallet. Please try again.</p>`;
                // Keep the overlay if an error occurs so user knows it's not ready
                revealOverlay.style.display = 'flex';
            }
        });

        // Function to reveal/hide the recovery phrase
        function toggleRevealPhrase() {
            const phraseContainer = document.getElementById('recovery-phrase-container');
            const revealOverlay = document.getElementById('reveal-overlay');

            phraseContainer.classList.toggle('blur-text');
            phraseContainer.classList.toggle('revealed');

            if (phraseContainer.classList.contains('revealed')) {
                revealOverlay.style.display = 'none';
            } else {
                revealOverlay.style.display = 'flex';
            }
        }

        // Function to copy the recovery phrase to clipboard
        function copyToClipboard() {
            if (!generatedSeedPhrase) {
                alert('No recovery phrase generated yet.');
                return;
            }

            // Construct the phrase with numbers for copying
            const wordsWithNumbers = generatedSeedPhrase.split(' ').map((word, index) => `${index + 1}. ${word}`).join('\n');
            
            // Use the modern clipboard API if available
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(wordsWithNumbers).then(() => {
                    alert('Recovery phrase copied to clipboard!');
                }).catch(err => {
                    console.error('Failed to copy text: ', err);
                    alert('Failed to copy recovery phrase.');
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = wordsWithNumbers;
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    alert('Recovery phrase copied to clipboard!');
                } catch (err) {
                    console.error('Failed to copy text: ', err);
                    alert('Failed to copy recovery phrase.');
                }
                document.body.removeChild(textArea);
            }
        }

        // Function to enable/disable the continue button based on checkbox
        function toggleContinueButton() {
            const checkbox = document.getElementById('understand-checkbox');
            const continueButton = document.getElementById('continue-button');
            if (checkbox.checked) {
                continueButton.disabled = false;
                continueButton.classList.remove('opacity-50', 'cursor-not-allowed');
                continueButton.classList.add('hover:bg-blue-700', 'focus:outline-none', 'focus:ring-2', 'focus:ring-blue-500', 'focus:ring-opacity-50', 'transition', 'duration-200');
            } else {
                continueButton.disabled = true;
                continueButton.classList.add('opacity-50', 'cursor-not-allowed');
                continueButton.classList.remove('hover:bg-blue-700', 'focus:outline-none', 'focus:ring-2', 'focus:ring-blue-500', 'focus:ring-opacity-50', 'transition', 'duration-200');
            }
        }
    </script>
</body>
</html>